import { Metadata } from 'next'
import { notFound } from 'next/navigation'
import { Header } from '../../../components/layout/header'
import { InfluencerProfile } from '../../../components/influencer/influencer-profile'
import { ChatInterface } from '../../../components/chat/chat-interface'
import { getInfluencer, getInfluencers } from '../../../lib/data/influencers'

interface PageProps {
  params: Promise<{
    id: string
  }>
}

// Generate static params for all influencers
export async function generateStaticParams() {
  const influencers = await getInfluencers()
  
  return influencers.map((influencer) => ({
    id: influencer.id,
  }))
}

// Generate metadata for each influencer page
export async function generateMetadata({ params }: PageProps): Promise<Metadata> {
  const { id } = await params
  const influencer = await getInfluencer(id)
  
  if (!influencer) {
    return {
      title: 'Influencer Not Found',
    }
  }

  return {
    title: `${influencer.name} | ATE Shop`,
    description: influencer.bio,
    openGraph: {
      title: `Chat with ${influencer.name}`,
      description: influencer.bio,
      images: [influencer.avatar],
    },
  }
}

export default async function InfluencerPage({ params }: PageProps) {
  const { id } = await params
  const influencer = await getInfluencer(id)
  
  if (!influencer) {
    notFound()
  }

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      <main className="container mx-auto px-4 py-8">
        <div className="grid lg:grid-cols-3 gap-8">
          {/* Influencer Profile Sidebar */}
          <div className="lg:col-span-1">
            <InfluencerProfile influencer={influencer} />
          </div>
          
          {/* Chat Interface */}
          <div className="lg:col-span-2">
            <ChatInterface influencer={influencer} />
          </div>
        </div>
      </main>
    </div>
  )
}