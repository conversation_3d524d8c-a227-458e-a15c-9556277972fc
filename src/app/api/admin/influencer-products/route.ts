import { NextRequest, NextResponse } from 'next/server'
import { supabaseAdmin } from '../../../../lib/supabase/server'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const influencerId = searchParams.get('influencer_id')

    if (!influencerId) {
      return NextResponse.json(
        { success: false, error: 'Influencer ID is required' },
        { status: 400 }
      )
    }

    const { data, error } = await supabaseAdmin
      .from('influencer_products')
      .select(`
        *,
        products (
          id, name, brand, category, image_url, price, currency
        )
      `)
      .eq('influencer_id', influencerId)
      .order('recommendation_priority', { ascending: false })

    if (error) throw error

    return NextResponse.json({ success: true, data })
  } catch (error) {
    console.error('Error fetching influencer products:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch influencer products' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      influencer_id,
      product_id,
      affiliate_url,
      commission_rate,
      custom_description,
      recommendation_priority
    } = body

    const { data, error } = await supabaseAdmin
      .from('influencer_products')
      .insert({
        influencer_id,
        product_id,
        affiliate_url,
        commission_rate,
        custom_description,
        recommendation_priority: recommendation_priority || 0
      })
      .select(`
        *,
        products (
          id, name, brand, category, image_url, price, currency
        )
      `)
      .single()

    if (error) throw error

    return NextResponse.json({ success: true, data })
  } catch (error) {
    console.error('Error creating influencer product:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to create influencer product' },
      { status: 500 }
    )
  }
}
