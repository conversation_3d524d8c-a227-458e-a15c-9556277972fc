// src/app/api/admin/influencers/[id]/route.ts - Fixed for Next.js 15
import { NextRequest, NextResponse } from 'next/server'
import { supabaseAdmin } from '../../../../../lib/supabase/server'

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const body = await request.json()
    const { id } = await params // ✅ Now properly awaited

    const { data, error } = await supabaseAdmin
      .from('influencers')
      .update({
        ...body,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single()

    if (error) throw error

    return NextResponse.json({ success: true, data })
  } catch (error) {
    console.error('Error updating influencer:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to update influencer' },
      { status: 500 }
    )
  }
}