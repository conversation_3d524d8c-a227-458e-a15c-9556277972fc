import { NextRequest, NextResponse } from 'next/server'
import { supabaseAdmin } from '../../../../lib/supabase/server'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const influencerId = searchParams.get('influencer_id')

    if (!influencerId) {
      return NextResponse.json(
        { success: false, error: 'Influencer ID is required' },
        { status: 400 }
      )
    }

    const { data, error } = await supabaseAdmin
      .from('ai_training_data')
      .select('*')
      .eq('influencer_id', influencerId)
      .eq('is_active', true)
      .order('created_at', { ascending: false })

    if (error) throw error

    return NextResponse.json({ success: true, data })
  } catch (error) {
    console.error('Error fetching training data:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch training data' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      influencer_id,
      category,
      user_input,
      ai_response,
      context_tags
    } = body

    const { data, error } = await supabaseAdmin
      .from('ai_training_data')
      .insert({
        influencer_id,
        category,
        user_input,
        ai_response,
        context_tags: context_tags || []
      })
      .select()
      .single()

    if (error) throw error

    return NextResponse.json({ success: true, data })
  } catch (error) {
    console.error('Error creating training data:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to create training data' },
      { status: 500 }
    )
  }
}
