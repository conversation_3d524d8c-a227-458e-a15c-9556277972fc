// src/app/admin/page.tsx - Complete updated version with image uploads
"use client"

import { useState, useEffect } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Plus, Search, Upload, BarChart3, MessageCircle, Edit } from 'lucide-react'

// Import the new components
import { ImageUpload } from '@/components/admin/ImageUpload'

// Types
interface Influencer {
  id: string
  name: string
  username: string
  specialty: string
  followers: string
  verified: boolean
  is_active: boolean
  total_recommendations: number
  avatar?: string
  cover_image?: string
  bio?: string
  personality_prompt?: string
}

interface Product {
  id: string
  name: string
  brand: string
  category: string
  subcategory?: string
  description?: string
  price: number
  currency: string
  image_url: string
  base_affiliate_url?: string
  in_stock: boolean
  labels?: string[]
  style_tags?: string[]
  color_tags?: string[]
}

interface Analytics {
  totalChats: number
  avgResponseTime: number
  totalProductsRecommended: number
  avgRating: number
}

interface AffiliateLink {
  id: string
  influencer_id: string
  product_id: string
  affiliate_url: string
  commission_rate: number
  custom_description: string
  recommendation_priority: number
}

interface TrainingData {
  id: string
  influencer_id: string
  category: string
  user_input: string
  ai_response: string
  context_tags: string[]
}

// Inline Influencer Form Component (since you might not have separate file)
function InfluencerForm({ 
  influencer, 
  onClose, 
  onSave 
}: { 
  influencer?: Influencer | null
  onClose: () => void
  onSave: () => void 
}) {
  const [formData, setFormData] = useState({
    id: influencer?.id || '',
    name: influencer?.name || '',
    username: influencer?.username || '',
    specialty: influencer?.specialty || '',
    followers: influencer?.followers || '',
    avatar: influencer?.avatar || '',
    cover_image: influencer?.cover_image || '',
    bio: influencer?.bio || '',
    personality_prompt: influencer?.personality_prompt || ''
  })

  const [saving, setSaving] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setSaving(true)
    
    try {
      const url = influencer 
        ? `/api/admin/influencers/${influencer.id}`
        : '/api/admin/influencers'
      
      const method = influencer ? 'PUT' : 'POST'
      
      const response = await fetch(url, {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData)
      })

      const data = await response.json()
      if (data.success) {
        onSave()
        onClose()
      } else {
        alert('Error saving influencer: ' + data.error)
      }
    } catch (error) {
      console.error('Error saving influencer:', error)
      alert('Error saving influencer')
    } finally {
      setSaving(false)
    }
  }

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
      <Card className="max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        <CardContent className="p-6">
          <form onSubmit={handleSubmit} className="space-y-6">
            <h3 className="text-lg font-semibold">
              {influencer ? 'Edit' : 'Add'} Influencer
            </h3>

            {/* Images Section */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <ImageUpload
                currentImage={formData.avatar}
                onImageChange={(url) => setFormData({...formData, avatar: url})}
                type="avatar"
                influencerId={formData.id || 'new'}
                label="Profile Avatar"
              />
              
              <ImageUpload
                currentImage={formData.cover_image}
                onImageChange={(url) => setFormData({...formData, cover_image: url})}
                type="cover"
                influencerId={formData.id || 'new'}
                label="Cover Image"
              />
            </div>

            {/* Basic Info */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium">Influencer ID</label>
                <Input
                  placeholder="alix-earle"
                  value={formData.id}
                  onChange={(e) => setFormData({...formData, id: e.target.value})}
                  disabled={!!influencer}
                  required
                />
              </div>
              
              <div>
                <label className="text-sm font-medium">Name</label>
                <Input
                  placeholder="Alix Earle"
                  value={formData.name}
                  onChange={(e) => setFormData({...formData, name: e.target.value})}
                  required
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium">Username</label>
                <Input
                  placeholder="alixearle"
                  value={formData.username}
                  onChange={(e) => setFormData({...formData, username: e.target.value})}
                  required
                />
              </div>
              
              <div>
                <label className="text-sm font-medium">Specialty</label>
                <Input
                  placeholder="Lifestyle & Fashion"
                  value={formData.specialty}
                  onChange={(e) => setFormData({...formData, specialty: e.target.value})}
                  required
                />
              </div>
            </div>

            <div>
              <label className="text-sm font-medium">Followers</label>
              <Input
                placeholder="5.2M"
                value={formData.followers}
                onChange={(e) => setFormData({...formData, followers: e.target.value})}
                required
              />
            </div>

            <div>
              <label className="text-sm font-medium">Bio</label>
              <textarea
                className="w-full p-2 border rounded"
                placeholder="Miami-based lifestyle influencer..."
                rows={3}
                value={formData.bio}
                onChange={(e) => setFormData({...formData, bio: e.target.value})}
              />
            </div>

            <div>
              <label className="text-sm font-medium">AI Personality Prompt</label>
              <textarea
                className="w-full p-2 border rounded font-mono text-sm"
                placeholder="You are [Name], the [description]..."
                rows={6}
                value={formData.personality_prompt}
                onChange={(e) => setFormData({...formData, personality_prompt: e.target.value})}
              />
            </div>

            <div className="flex justify-end space-x-2">
              <Button type="button" variant="outline" onClick={onClose}>
                Cancel
              </Button>
              <Button type="submit" disabled={saving}>
                {saving ? 'Saving...' : (influencer ? 'Update' : 'Create')}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  )
}

// Product Form Component
function ProductForm({ 
  product, 
  onClose, 
  onSave 
}: { 
  product?: Product | null
  onClose: () => void
  onSave: () => void 
}) {
  const [formData, setFormData] = useState({
    name: product?.name || '',
    brand: product?.brand || '',
    category: product?.category || '',
    subcategory: product?.subcategory || '',
    description: product?.description || '',
    price: product?.price || 0,
    currency: product?.currency || 'USD',
    image_url: product?.image_url || '',
    base_affiliate_url: product?.base_affiliate_url || '',
    labels: product?.labels?.join(', ') || '',
    style_tags: product?.style_tags?.join(', ') || '',
    color_tags: product?.color_tags?.join(', ') || '',
    in_stock: product?.in_stock ?? true
  })

  const [saving, setSaving] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setSaving(true)
    
    try {
      const payload = {
        ...formData,
        labels: formData.labels.split(',').map((l: string) => l.trim()).filter(Boolean),
        style_tags: formData.style_tags.split(',').map((l: string) => l.trim()).filter(Boolean),
        color_tags: formData.color_tags.split(',').map((l: string) => l.trim()).filter(Boolean),
      }

      const url = product 
        ? `/api/admin/products/${product.id}`
        : '/api/admin/products'
      
      const method = product ? 'PUT' : 'POST'
      
      const response = await fetch(url, {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload)
      })

      const data = await response.json()
      if (data.success) {
        onSave()
        onClose()
      } else {
        alert('Error saving product: ' + data.error)
      }
    } catch (error) {
      console.error('Error saving product:', error)
      alert('Error saving product')
    } finally {
      setSaving(false)
    }
  }

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <Card className="w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <CardHeader>
          <CardTitle>{product ? 'Edit Product' : 'Add Product'}</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium">Product Name</label>
                <Input
                  required
                  value={formData.name}
                  onChange={(e) => setFormData({...formData, name: e.target.value})}
                />
              </div>
              <div>
                <label className="text-sm font-medium">Brand</label>
                <Input
                  required
                  value={formData.brand}
                  onChange={(e) => setFormData({...formData, brand: e.target.value})}
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium">Category</label>
                <Input
                  required
                  value={formData.category}
                  onChange={(e) => setFormData({...formData, category: e.target.value})}
                />
              </div>
              <div>
                <label className="text-sm font-medium">Subcategory</label>
                <Input
                  value={formData.subcategory}
                  onChange={(e) => setFormData({...formData, subcategory: e.target.value})}
                />
              </div>
            </div>

            <div>
              <label className="text-sm font-medium">Description</label>
              <textarea
                className="w-full p-2 border rounded"
                rows={3}
                value={formData.description}
                onChange={(e) => setFormData({...formData, description: e.target.value})}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium">Price</label>
                <Input
                  type="number"
                  step="0.01"
                  required
                  value={formData.price}
                  onChange={(e) => setFormData({...formData, price: parseFloat(e.target.value)})}
                />
              </div>
              <div>
                <label className="text-sm font-medium">Currency</label>
                <Input
                  value={formData.currency}
                  onChange={(e) => setFormData({...formData, currency: e.target.value})}
                />
              </div>
            </div>

            <div>
              <label className="text-sm font-medium">Image URL</label>
              <Input
                value={formData.image_url}
                onChange={(e) => setFormData({...formData, image_url: e.target.value})}
              />
            </div>

            <div>
              <label className="text-sm font-medium">Base Affiliate URL</label>
              <Input
                value={formData.base_affiliate_url}
                onChange={(e) => setFormData({...formData, base_affiliate_url: e.target.value})}
              />
            </div>

            <div>
              <label className="text-sm font-medium">Labels (comma separated)</label>
              <Input
                value={formData.labels}
                onChange={(e) => setFormData({...formData, labels: e.target.value})}
                placeholder="trending, bestseller, sale"
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium">Style Tags (comma separated)</label>
                <Input
                  value={formData.style_tags}
                  onChange={(e) => setFormData({...formData, style_tags: e.target.value})}
                  placeholder="casual, elegant, boho"
                />
              </div>
              <div>
                <label className="text-sm font-medium">Color Tags (comma separated)</label>
                <Input
                  value={formData.color_tags}
                  onChange={(e) => setFormData({...formData, color_tags: e.target.value})}
                  placeholder="black, white, red"
                />
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="in_stock"
                checked={formData.in_stock}
                onChange={(e) => setFormData({...formData, in_stock: e.target.checked})}
              />
              <label htmlFor="in_stock" className="text-sm font-medium">In Stock</label>
            </div>

            <div className="flex justify-end space-x-2">
              <Button type="button" variant="outline" onClick={onClose}>
                Cancel
              </Button>
              <Button type="submit" disabled={saving}>
                {saving ? 'Saving...' : (product ? 'Update' : 'Create')}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  )
}

// Affiliate Link Form Component
function AffiliateLinkForm({ 
  link, 
  influencers,
  products,
  selectedInfluencer,
  onClose, 
  onSave 
}: { 
  link?: AffiliateLink | null
  influencers: Influencer[]
  products: Product[]
  selectedInfluencer: string
  onClose: () => void
  onSave: () => void 
}) {
  const [formData, setFormData] = useState({
    influencer_id: link?.influencer_id || selectedInfluencer,
    product_id: link?.product_id || '',
    affiliate_url: link?.affiliate_url || '',
    commission_rate: link?.commission_rate || 0,
    custom_description: link?.custom_description || '',
    recommendation_priority: link?.recommendation_priority || 1
  })

  const [saving, setSaving] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setSaving(true)
    
    try {
      const url = link 
        ? `/api/admin/influencer-products/${link.id}`
        : '/api/admin/influencer-products'
      
      const method = link ? 'PUT' : 'POST'
      
      const response = await fetch(url, {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData)
      })

      const data = await response.json()
      if (data.success) {
        onSave()
        onClose()
      } else {
        alert('Error saving affiliate link: ' + data.error)
      }
    } catch (error) {
      console.error('Error saving affiliate link:', error)
      alert('Error saving affiliate link')
    } finally {
      setSaving(false)
    }
  }

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <Card className="w-full max-w-lg">
        <CardHeader>
          <CardTitle>{link ? 'Edit Affiliate Link' : 'Add Affiliate Link'}</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label className="text-sm font-medium">Influencer</label>
              <select
                className="w-full p-2 border rounded"
                value={formData.influencer_id}
                onChange={(e) => setFormData({...formData, influencer_id: e.target.value})}
                required
              >
                {influencers.map((influencer) => (
                  <option key={influencer.id} value={influencer.id}>
                    {influencer.name}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="text-sm font-medium">Product</label>
              <select
                className="w-full p-2 border rounded"
                value={formData.product_id}
                onChange={(e) => setFormData({...formData, product_id: e.target.value})}
                required
              >
                <option value="">Select a product</option>
                {products.map((product) => (
                  <option key={product.id} value={product.id}>
                    {product.name} - {product.brand}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="text-sm font-medium">Affiliate URL</label>
              <Input
                required
                value={formData.affiliate_url}
                onChange={(e) => setFormData({...formData, affiliate_url: e.target.value})}
                placeholder="https://..."
              />
            </div>

            <div>
              <label className="text-sm font-medium">Commission Rate (%)</label>
              <Input
                type="number"
                step="0.1"
                value={formData.commission_rate}
                onChange={(e) => setFormData({...formData, commission_rate: parseFloat(e.target.value)})}
              />
            </div>

            <div>
              <label className="text-sm font-medium">Custom Description</label>
              <textarea
                className="w-full p-2 border rounded"
                rows={3}
                value={formData.custom_description}
                onChange={(e) => setFormData({...formData, custom_description: e.target.value})}
                placeholder="Why this influencer recommends this product..."
              />
            </div>

            <div>
              <label className="text-sm font-medium">Recommendation Priority (1-10)</label>
              <Input
                type="number"
                min="1"
                max="10"
                value={formData.recommendation_priority}
                onChange={(e) => setFormData({...formData, recommendation_priority: parseInt(e.target.value)})}
              />
            </div>

            <div className="flex justify-end space-x-2">
              <Button type="button" variant="outline" onClick={onClose}>
                Cancel
              </Button>
              <Button type="submit" disabled={saving}>
                {saving ? 'Saving...' : (link ? 'Update' : 'Create')}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  )
}

// AI Training Form Component
function TrainingDataForm({ 
  training, 
  influencers,
  selectedInfluencer,
  onClose, 
  onSave 
}: { 
  training?: TrainingData | null
  influencers: Influencer[]
  selectedInfluencer: string
  onClose: () => void
  onSave: () => void 
}) {
  const [formData, setFormData] = useState({
    influencer_id: training?.influencer_id || selectedInfluencer,
    category: training?.category || '',
    user_input: training?.user_input || '',
    ai_response: training?.ai_response || '',
    context_tags: training?.context_tags?.join(', ') || ''
  })

  const [saving, setSaving] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setSaving(true)
    
    try {
      const payload = {
        ...formData,
        context_tags: formData.context_tags.split(',').map(t => t.trim()).filter(Boolean)
      }

      const url = training 
        ? `/api/admin/training-data/${training.id}`
        : '/api/admin/training-data'
      
      const method = training ? 'PUT' : 'POST'
      
      const response = await fetch(url, {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload)
      })

      const data = await response.json()
      if (data.success) {
        onSave()
        onClose()
      } else {
        alert('Error saving training data: ' + data.error)
      }
    } catch (error) {
      console.error('Error saving training data:', error)
      alert('Error saving training data')
    } finally {
      setSaving(false)
    }
  }

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <Card className="w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <CardHeader>
          <CardTitle>{training ? 'Edit Training Data' : 'Add Training Example'}</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label className="text-sm font-medium">Influencer</label>
              <select
                className="w-full p-2 border rounded"
                value={formData.influencer_id}
                onChange={(e) => setFormData({...formData, influencer_id: e.target.value})}
                required
              >
                {influencers.map((influencer) => (
                  <option key={influencer.id} value={influencer.id}>
                    {influencer.name}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="text-sm font-medium">Category</label>
              <select
                className="w-full p-2 border rounded"
                value={formData.category}
                onChange={(e) => setFormData({...formData, category: e.target.value})}
                required
              >
                <option value="">Select category</option>
                <option value="product_recommendation">Product Recommendation</option>
                <option value="style_advice">Style Advice</option>
                <option value="general_chat">General Chat</option>
                <option value="size_guidance">Size Guidance</option>
                <option value="occasion_styling">Occasion Styling</option>
              </select>
            </div>

            <div>
              <label className="text-sm font-medium">User Input</label>
              <textarea
                className="w-full p-2 border rounded font-mono text-sm"
                rows={4}
                required
                value={formData.user_input}
                onChange={(e) => setFormData({...formData, user_input: e.target.value})}
                placeholder="What the user asks or says..."
              />
            </div>

            <div>
              <label className="text-sm font-medium">AI Response</label>
              <textarea
                className="w-full p-2 border rounded font-mono text-sm"
                rows={6}
                required
                value={formData.ai_response}
                onChange={(e) => setFormData({...formData, ai_response: e.target.value})}
                placeholder="How the influencer should respond..."
              />
            </div>

            <div>
              <label className="text-sm font-medium">Context Tags (comma separated)</label>
              <Input
                value={formData.context_tags}
                onChange={(e) => setFormData({...formData, context_tags: e.target.value})}
                placeholder="casual, summer, budget-friendly, formal"
              />
            </div>

            <div className="flex justify-end space-x-2">
              <Button type="button" variant="outline" onClick={onClose}>
                Cancel
              </Button>
              <Button type="submit" disabled={saving}>
                {saving ? 'Saving...' : (training ? 'Update' : 'Create')}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  )
}

export default function AdminDashboard() {
  const [activeTab, setActiveTab] = useState('overview')
  const [influencers, setInfluencers] = useState<Influencer[]>([])
  const [products, setProducts] = useState<Product[]>([])
  const [selectedInfluencer, setSelectedInfluencer] = useState<string>('')
  const [loading, setLoading] = useState(false)
  const [analytics, setAnalytics] = useState<Analytics | null>(null)
  const [showInfluencerForm, setShowInfluencerForm] = useState(false)
  const [editingInfluencer, setEditingInfluencer] = useState<Influencer | null>(null)
  
  // New state for additional forms
  const [showProductForm, setShowProductForm] = useState(false)
  const [editingProduct, setEditingProduct] = useState<Product | null>(null)
  const [showAffiliateForm, setShowAffiliateForm] = useState(false)
  const [editingAffiliateLink, setEditingAffiliateLink] = useState<AffiliateLink | null>(null)
  const [showTrainingForm, setShowTrainingForm] = useState(false)
  const [editingTrainingData, setEditingTrainingData] = useState<TrainingData | null>(null)

  useEffect(() => {
    loadInfluencers()
    loadAnalytics()
  }, [])

  useEffect(() => {
    if (activeTab === 'products') {
      loadProducts()
    }
  }, [activeTab])

  const loadInfluencers = async () => {
    try {
      const response = await fetch('/api/admin/influencers')
      const data = await response.json()
      if (data.success) {
        setInfluencers(data.data)
        if (data.data.length > 0) {
          setSelectedInfluencer(data.data[0].id)
        }
      }
    } catch (error) {
      console.error('Error loading influencers:', error)
    }
  }

  const loadProducts = async (search = '') => {
    try {
      const params = new URLSearchParams()
      if (search) params.append('search', search)
      
      const response = await fetch(`/api/admin/products?${params}`)
      const data = await response.json()
      if (data.success) {
        setProducts(data.data)
      }
    } catch (error) {
      console.error('Error loading products:', error)
    }
  }

  const loadAnalytics = async () => {
    try {
      const response = await fetch('/api/admin/analytics?days=7')
      const data = await response.json()
      if (data.success) {
        setAnalytics(data.data)
      }
    } catch (error) {
      console.error('Error loading analytics:', error)
    }
  }

  const handleEditInfluencer = (influencer: Influencer) => {
    setEditingInfluencer(influencer)
    setShowInfluencerForm(true)
  }

  const handleAddInfluencer = () => {
    setEditingInfluencer(null)
    setShowInfluencerForm(true)
  }

  const handleFormClose = () => {
    setShowInfluencerForm(false)
    setEditingInfluencer(null)
  }

  const handleFormSave = () => {
    loadInfluencers()
    handleFormClose()
  }

  // New handlers for additional forms
  const handleAddProduct = () => {
    setEditingProduct(null)
    setShowProductForm(true)
  }

  const handleProductFormClose = () => {
    setShowProductForm(false)
    setEditingProduct(null)
  }

  const handleProductFormSave = () => {
    loadProducts()
    handleProductFormClose()
  }

  const handleAddAffiliateLink = () => {
    setEditingAffiliateLink(null)
    setShowAffiliateForm(true)
  }

  const handleAffiliateLinkFormClose = () => {
    setShowAffiliateForm(false)
    setEditingAffiliateLink(null)
  }

  const handleAffiliateLinkFormSave = () => {
    handleAffiliateLinkFormClose()
  }

  const handleAddTrainingExample = () => {
    setEditingTrainingData(null)
    setShowTrainingForm(true)
  }

  const handleTrainingFormClose = () => {
    setShowTrainingForm(false)
    setEditingTrainingData(null)
  }

  const handleTrainingFormSave = () => {
    handleTrainingFormClose()
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="border-b">
        <div className="container mx-auto px-4 py-4">
          <h1 className="text-3xl font-bold">ATE Shop Admin</h1>
          <p className="text-muted-foreground">Manage influencers, products, and AI training</p>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="influencers">Influencers</TabsTrigger>
            <TabsTrigger value="products">Products</TabsTrigger>
            <TabsTrigger value="affiliate">Affiliate Links</TabsTrigger>
            <TabsTrigger value="training">AI Training</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Influencers</CardTitle>
                  <MessageCircle className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{influencers.length}</div>
                  <p className="text-xs text-muted-foreground">
                    {influencers.filter(i => i.is_active).length} active
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Chats (7d)</CardTitle>
                  <BarChart3 className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{analytics?.totalChats || 0}</div>
                  <p className="text-xs text-muted-foreground">
                    Avg response: {Math.round(analytics?.avgResponseTime || 0)}ms
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Products Recommended</CardTitle>
                  <Plus className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{analytics?.totalProductsRecommended || 0}</div>
                  <p className="text-xs text-muted-foreground">
                    Last 7 days
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Avg Rating</CardTitle>
                  <BarChart3 className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {analytics?.avgRating ? analytics.avgRating.toFixed(1) : 'N/A'}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    User feedback
                  </p>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="influencers" className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-2xl font-bold">Influencers</h2>
              <Button onClick={handleAddInfluencer}>
                <Plus className="h-4 w-4 mr-2" />
                Add Influencer
              </Button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {influencers.map((influencer) => (
                <Card key={influencer.id} className="cursor-pointer hover:shadow-lg transition-shadow">
                  <CardContent className="p-0">
                    {/* Cover Image */}
                    <div className="h-32 bg-gradient-to-r from-primary/20 to-purple-500/20 relative overflow-hidden">
                      {influencer.cover_image ? (
                        <img 
                          src={influencer.cover_image} 
                          alt={`${influencer.name} cover`}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <div className="w-full h-full bg-gradient-to-r from-primary/20 to-purple-500/20" />
                      )}
                      <div className="absolute top-4 right-4 flex gap-2">
                        {influencer.verified && (
                          <Badge className="bg-white/20 text-white border-white/30">
                            ✓ Verified
                          </Badge>
                        )}
                        <Button
                          variant="secondary"
                          size="sm"
                          onClick={() => handleEditInfluencer(influencer)}
                          className="bg-white/20 text-white border-white/30 hover:bg-white/30"
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>

                    <div className="p-6">
                      {/* Avatar and Basic Info */}
                      <div className="flex items-center space-x-4 mb-4 -mt-8">
                        <div className="relative">
                          {influencer.avatar ? (
                            <img
                              src={influencer.avatar}
                              alt={influencer.name}
                              className="w-16 h-16 rounded-full border-4 border-background shadow-lg relative z-10"
                            />
                          ) : (
                            <div className="w-16 h-16 bg-primary/10 rounded-full border-4 border-background shadow-lg relative z-10 flex items-center justify-center">
                              {influencer.name.charAt(0)}
                            </div>
                          )}
                          {influencer.is_active && (
                            <div className="absolute -bottom-1 -right-1 w-5 h-5 bg-green-500 rounded-full border-2 border-background" />
                          )}
                        </div>
                        <div className="flex-1 mt-8">
                          <h3 className="font-semibold text-foreground">{influencer.name}</h3>
                          <p className="text-sm text-muted-foreground">@{influencer.username}</p>
                        </div>
                      </div>
                      
                      <div className="space-y-2">
                        <p className="text-sm"><strong>Specialty:</strong> {influencer.specialty}</p>
                        <p className="text-sm"><strong>Followers:</strong> {influencer.followers}</p>
                        <p className="text-sm"><strong>Recommendations:</strong> {influencer.total_recommendations}</p>
                        {influencer.bio && (
                          <p className="text-sm text-muted-foreground line-clamp-2">{influencer.bio}</p>
                        )}
                      </div>

                      <div className="flex justify-between items-center mt-4">
                        <Badge variant={influencer.is_active ? "default" : "secondary"}>
                          {influencer.is_active ? "Active" : "Inactive"}
                        </Badge>
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => handleEditInfluencer(influencer)}
                        >
                          Edit Profile
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="products" className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-2xl font-bold">Products</h2>
              <div className="flex space-x-2">
                <Button variant="outline">
                  <Upload className="h-4 w-4 mr-2" />
                  Bulk Import
                </Button>
                <Button onClick={handleAddProduct}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Product
                </Button>
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <Search className="h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search products..."
                onChange={(e) => loadProducts(e.target.value)}
                className="max-w-sm"
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {products.map((product) => (
                <Card key={product.id}>
                  <CardContent className="p-4">
                    {product.image_url && (
                      <img 
                        src={product.image_url} 
                        alt={product.name}
                        className="w-full h-32 object-cover rounded mb-4"
                      />
                    )}
                    <h3 className="font-semibold mb-2">{product.name}</h3>
                    <p className="text-sm text-muted-foreground mb-2">{product.brand}</p>
                    <p className="font-bold text-primary mb-2">
                      ${product.price} {product.currency}
                    </p>
                    <div className="flex flex-wrap gap-1 mb-2">
                      {product.labels?.slice(0, 3).map((label) => (
                        <Badge key={label} variant="outline" className="text-xs">
                          {label}
                        </Badge>
                      ))}
                    </div>
                    <Badge variant={product.in_stock ? "default" : "secondary"}>
                      {product.in_stock ? "In Stock" : "Out of Stock"}
                    </Badge>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="affiliate" className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-2xl font-bold">Affiliate Links</h2>
              <Button onClick={handleAddAffiliateLink}>
                <Plus className="h-4 w-4 mr-2" />
                Add Product Link
              </Button>
            </div>

            <div className="flex items-center space-x-4">
              <label className="text-sm font-medium">Influencer:</label>
              <select 
                value={selectedInfluencer}
                onChange={(e) => setSelectedInfluencer(e.target.value)}
                className="border rounded px-3 py-1"
              >
                {influencers.map((influencer) => (
                  <option key={influencer.id} value={influencer.id}>
                    {influencer.name}
                  </option>
                ))}
              </select>
            </div>

            <div className="text-center py-8 text-muted-foreground">
              Select an influencer to view their affiliate products
            </div>
          </TabsContent>

          <TabsContent value="training" className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-2xl font-bold">AI Training Data</h2>
              <Button onClick={handleAddTrainingExample}>
                <Plus className="h-4 w-4 mr-2" />
                Add Training Example
              </Button>
            </div>

            <div className="flex items-center space-x-4">
              <label className="text-sm font-medium">Influencer:</label>
              <select 
                value={selectedInfluencer}
                onChange={(e) => setSelectedInfluencer(e.target.value)}
                className="border rounded px-3 py-1"
              >
                {influencers.map((influencer) => (
                  <option key={influencer.id} value={influencer.id}>
                    {influencer.name}
                  </option>
                ))}
              </select>
              <Badge variant="secondary">
                0 examples
              </Badge>
            </div>

            <div className="text-center py-8 text-muted-foreground">
              No training data found. Add some examples to improve AI responses.
            </div>
          </TabsContent>
        </Tabs>
      </div>

      {/* Influencer Form Modal */}
      {showInfluencerForm && (
        <InfluencerForm 
          influencer={editingInfluencer}
          onClose={handleFormClose}
          onSave={handleFormSave}
        />
      )}

      {/* Product Form Modal */}
      {showProductForm && (
        <ProductForm 
          product={editingProduct}
          onClose={handleProductFormClose}
          onSave={handleProductFormSave}
        />
      )}

      {/* Affiliate Link Form Modal */}
      {showAffiliateForm && (
        <AffiliateLinkForm 
          link={editingAffiliateLink}
          influencers={influencers}
          products={products}
          selectedInfluencer={selectedInfluencer}
          onClose={handleAffiliateLinkFormClose}
          onSave={handleAffiliateLinkFormSave}
        />
      )}

      {/* Training Data Form Modal */}
      {showTrainingForm && (
        <TrainingDataForm 
          training={editingTrainingData}
          influencers={influencers}
          selectedInfluencer={selectedInfluencer}
          onClose={handleTrainingFormClose}
          onSave={handleTrainingFormSave}
        />
      )}
    </div>
  )
}