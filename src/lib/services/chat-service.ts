import { ChatResponse, ProductRecommendation, Product } from '../../types'
import { sampleProducts } from '../data/products'
import { nanoid } from 'nanoid'

interface ProcessMessageParams {
  message: string
  influencerId: string
  conversationId?: string
}

class ChatService {
  async processMessage({ message, influencerId }: ProcessMessageParams): Promise<ChatResponse> {
    // Simulate AI processing delay
    await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 1000))

    // Generate contextual response based on message content
    const response = this.generateContextualResponse(influencerId, message)
    
    return {
      content: response.content,
      products: response.products,
      confidence: 0.85,
      messageId: nanoid(),
    }
  }

  private generateContextualResponse(influencerId: string, message: string): {
    content: string
    products: ProductRecommendation[]
  } {
    const lowerMessage = message.toLowerCase()
    
    // Beach/vacation context
    if (lowerMessage.includes('beach') || lowerMessage.includes('vacation') || lowerMessage.includes('summer')) {
      return {
        content: "Perfect! For a beach day, I'm thinking light, breezy pieces that still look put-together. Here are some of my current faves that would be absolutely perfect for you! ✨",
        products: this.createProductRecommendations(sampleProducts.beach, 'beach')
      }
    }
    
    // Casual context
    if (lowerMessage.includes('casual') || lowerMessage.includes('weekend') || lowerMessage.includes('comfortable')) {
      return {
        content: "Love a good casual vibe! These pieces are perfect for looking effortlessly chic while staying comfy. You'll feel amazing in these! 💫",
        products: this.createProductRecommendations(sampleProducts.casual, 'casual')
      }
    }
    
    // Luxury context
    if (lowerMessage.includes('special') || lowerMessage.includes('luxury') || lowerMessage.includes('investment')) {
      return {
        content: "Ooh, treating yourself to something special? I love that! These are some investment pieces that will elevate your entire wardrobe. Quality over quantity, always! ✨",
        products: this.createProductRecommendations(sampleProducts.luxury, 'luxury')
      }
    }
    
    // Date night context
    if (lowerMessage.includes('date') || lowerMessage.includes('night out') || lowerMessage.includes('dinner')) {
      return {
        content: "Date night calls for something that makes you feel absolutely stunning! These pieces are perfect for making a memorable impression while staying true to your style 💕",
        products: this.createProductRecommendations([...sampleProducts.beach, ...sampleProducts.luxury], 'date')
      }
    }
    
    // Default response
    return {
      content: "I love helping you put together the perfect look! Based on what you're looking for, here are some amazing pieces I think you'll absolutely love. Each one is carefully curated to match your style! 💫",
      products: this.createProductRecommendations([...sampleProducts.beach.slice(0, 1), ...sampleProducts.casual.slice(0, 1)], 'general')
    }
  }

  private createProductRecommendations(products: Product[], context: string): ProductRecommendation[] {
    const reasons = {
      beach: "Perfect for beach vibes - lightweight and stylish!",
      casual: "Great for a relaxed, effortless look!",
      luxury: "An investment piece that will elevate your entire wardrobe!",
      date: "Perfect for making a stunning impression!",
      general: "A versatile piece that matches your style perfectly!"
    }

    return products.slice(0, 2).map(product => ({
      product,
      reason: reasons[context as keyof typeof reasons] || reasons.general,
      confidence: 0.8 + Math.random() * 0.2
    }))
  }
}

export const chatService = new ChatService()