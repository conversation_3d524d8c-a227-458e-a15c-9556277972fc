// src/lib/services/enhanced-chat-service.ts - Updated with advanced prompts
import { ChatResponse, ProductRecommendation } from '../../types'
import { nanoid } from 'nanoid'
import OpenAI from 'openai'
import { supabaseAdmin } from '../supabase/server'
import { getInfluencerSystemPrompt, generateContextualPrompt } from '../prompts/influencer-prompts'

interface ProcessMessageParams {
  message: string
  influencerId: string
  conversationId?: string
  sessionId?: string
}

interface DatabaseProduct {
  id: string
  name: string
  description: string
  price: number
  currency: string
  brand: string
  category: string
  image_url: string
  affiliate_url: string
  in_stock: boolean
  rating: number
  review_count: number
  labels: string[]
  style_tags: string[]
  recommendation_priority: number
  custom_description?: string
}

interface InfluencerData {
  id: string
  name: string
  personality_prompt: string
  style_keywords: string[]
  categories: string[]
}

class EnhancedChatService {
  private openai: OpenAI

  constructor() {
    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    })
  }

  async processMessage({ 
    message, 
    influencerId, 
    conversationId, 
    sessionId = nanoid() 
  }: ProcessMessageParams): Promise<ChatResponse> {
    try {
      const startTime = Date.now()

      // 1. Get influencer data and personality
      const influencer = await this.getInfluencerData(influencerId)
      if (!influencer) {
        throw new Error('Influencer not found')
      }

      // 2. Analyze message intent and extract keywords
      const messageAnalysis = await this.analyzeMessage(message, influencer)

      // 3. Get relevant products based on analysis
      const products = await this.getRelevantProducts(
        influencerId, 
        messageAnalysis.keywords,
        messageAnalysis.intent
      )

      // 4. Generate AI response using advanced prompts
      const aiResponse = await this.generateAdvancedAIResponse(
        message,
        influencerId,
        products,
        messageAnalysis
      )

      // 5. Create product recommendations
      const productRecommendations = await this.createProductRecommendations(
        products,
        messageAnalysis,
        influencer
      )

      // 6. Log chat session for analytics
      await this.logChatSession({
        sessionId,
        influencerId,
        userMessage: message,
        aiResponse: aiResponse.content,
        productsRecommended: productRecommendations.map(p => p.product.id),
        responseTime: Date.now() - startTime
      })

      return {
        content: aiResponse.content,
        products: productRecommendations,
        confidence: aiResponse.confidence,
        messageId: nanoid(),
      }

    } catch (error) {
      console.error('Enhanced chat service error:', error)
      return this.getFallbackResponse(influencerId)
    }
  }

  private async generateAdvancedAIResponse(
    message: string,
    influencerId: string,
    products: DatabaseProduct[],
    messageAnalysis: any
  ) {
    // Use the advanced prompt system
    const contextualPrompt = generateContextualPrompt(
      influencerId,
      message,
      products,
      messageAnalysis
    )

    try {
      const response = await this.openai.chat.completions.create({
        model: "gpt-4o-mini",
        messages: [
          { role: "user", content: contextualPrompt }
        ],
        temperature: 0.8, // Higher temperature for more personality
        max_tokens: 150, // Shorter responses for chat feel
        presence_penalty: 0.3, // Encourage varied language
        frequency_penalty: 0.3 // Reduce repetition
      })

      const content = response.choices[0]?.message?.content || 
        this.getFallbackPersonalityResponse(influencerId)

      return {
        content,
        confidence: 0.85 + (products.length > 0 ? 0.1 : 0)
      }

    } catch (error) {
      console.error('AI response generation error:', error)
      return {
        content: this.getFallbackPersonalityResponse(influencerId),
        confidence: 0.6
      }
    }
  }

  private getFallbackPersonalityResponse(influencerId: string): string {
    const fallbacks = {
      'alix-earle': "Babe! I'm so excited to help you find the perfect pieces! Let me pull together some amazing options that'll look so good on you! ✨💕",
      'emma-chamberlain': "Okay literally I want to help you find the cutest stuff! Give me like two seconds to think of some options that would be perfect for you ☕️✨",
      'style-maven': "Darling, I'd be delighted to help you curate the perfect pieces for your wardrobe. Let me share some timeless options that will serve you beautifully. 🖤✨"
    }

    return fallbacks[influencerId as keyof typeof fallbacks] || 
           "I'm so excited to help you find the perfect style! Let me show you some amazing pieces! ✨"
  }

  // Keep all other existing methods (getInfluencerData, analyzeMessage, etc.)
  private async getInfluencerData(influencerId: string): Promise<InfluencerData | null> {
    try {
      const { data, error } = await supabaseAdmin
        .from('influencers')
        .select('id, name, personality_prompt, style_keywords, categories')
        .eq('id', influencerId)
        .single()

      if (error) throw error
      return data
    } catch (error) {
      console.error('Error fetching influencer data:', error)
      return null
    }
  }

  private async analyzeMessage(message: string, influencer: InfluencerData) {
    const analysisPrompt = `
Analyze this user message for fashion/shopping intent and extract relevant keywords.
User message: "${message}"
Influencer style: ${influencer.style_keywords.join(', ')}
Influencer categories: ${influencer.categories.join(', ')}

Return a JSON object with:
- intent: primary intent (outfit_request, product_search, style_advice, general_chat)
- occasion: detected occasion (casual, formal, date, work, beach, party, etc.)
- style_preferences: detected style keywords
- price_range: detected budget (budget, mid, luxury, or null)
- specific_items: specific clothing items mentioned
- keywords: relevant search keywords for products

Respond only with valid JSON.
`

    try {
      const response = await this.openai.chat.completions.create({
        model: "gpt-4o-mini",
        messages: [{ role: "user", content: analysisPrompt }],
        temperature: 0.3,
        max_tokens: 300
      })

      const content = response.choices[0]?.message?.content
      if (!content) throw new Error('No analysis response')

      return JSON.parse(content)
    } catch (error) {
      console.error('Message analysis error:', error)
      return {
        intent: 'outfit_request',
        occasion: this.extractOccasion(message),
        style_preferences: [],
        price_range: null,
        specific_items: [],
        keywords: this.extractSimpleKeywords(message)
      }
    }
  }

  private async getRelevantProducts(
    influencerId: string, 
    keywords: string[], 
    intent: string
  ): Promise<DatabaseProduct[]> {
    try {
      let query = supabaseAdmin
        .from('products')
        .select(`
          *,
          influencer_products!inner(
            affiliate_url,
            recommendation_priority,
            custom_description,
            is_active
          )
        `)
        .eq('influencer_products.influencer_id', influencerId)
        .eq('influencer_products.is_active', true)
        .eq('in_stock', true)
        .order('influencer_products.recommendation_priority', { ascending: false })
        .limit(10)

      if (keywords.length > 0) {
        query = query.or(
          keywords.map(keyword => 
            `labels.cs.{${keyword}},style_tags.cs.{${keyword}},category.ilike.%${keyword}%`
          ).join(',')
        )
      }

      const { data, error } = await query

      if (error) throw error
      
      return data.map((item: any) => ({
        ...item,
        affiliate_url: item.influencer_products.affiliate_url,
        recommendation_priority: item.influencer_products.recommendation_priority,
        custom_description: item.influencer_products.custom_description
      }))

    } catch (error) {
      console.error('Error fetching products:', error)
      return []
    }
  }

  private async createProductRecommendations(
    products: DatabaseProduct[],
    messageAnalysis: any,
    influencer: InfluencerData
  ): Promise<ProductRecommendation[]> {
    return products.slice(0, 3).map(product => {
      const reason = this.generateContextualRecommendationReason(product, messageAnalysis, influencer)
      
      return {
        product: {
          id: product.id,
          name: product.name,
          description: product.custom_description || product.description,
          price: product.price,
          currency: product.currency,
          brand: product.brand,
          category: product.category,
          image: product.image_url,
          affiliateUrl: product.affiliate_url,
          inStock: product.in_stock,
          rating: product.rating,
          reviewCount: product.review_count
        },
        reason,
        confidence: 0.7 + (product.recommendation_priority / 100)
      }
    })
  }

  private generateContextualRecommendationReason(
    product: DatabaseProduct, 
    messageAnalysis: any, 
    influencer: InfluencerData
  ): string {
    const influencerReasons = {
      'alix-earle': [
        `This is so Miami girl approved! Perfect for ${messageAnalysis.occasion || 'any vibe'} ✨`,
        `I'm literally obsessed with this ${product.category.toLowerCase()} - it's giving effortless chic!`,
        `Trust me on this one babe, ${product.brand} never misses!`,
        `This is it, this is the one! You're gonna look so good! 💕`
      ],
      'emma-chamberlain': [
        `Okay but this ${product.category.toLowerCase()} is actually so cute though`,
        `I'm not gonna lie, I'd totally wear this to a coffee shop date ☕️`,
        `This gives me vintage vibes but in the best way possible`,
        `Literally this is perfect for ${messageAnalysis.occasion || 'whatever vibe you\'re going for'}`
      ],
      'style-maven': [
        `The craftsmanship on this ${product.category.toLowerCase()} is exquisite, darling`,
        `This is a timeless investment piece that will serve you beautifully`,
        `${product.brand} is known for their attention to detail and quality`,
        `A sophisticated choice that embodies effortless elegance 🖤`
      ]
    }

    const reasons = influencerReasons[influencer.id as keyof typeof influencerReasons] || [
      `Perfect for ${messageAnalysis.occasion || 'your style'} - love this piece!`
    ]

    return reasons[Math.floor(Math.random() * reasons.length)]
  }

  private async logChatSession(data: {
    sessionId: string
    influencerId: string
    userMessage: string
    aiResponse: string
    productsRecommended: string[]
    responseTime: number
  }) {
    try {
      const { error } = await supabaseAdmin
        .from('chat_sessions')
        .insert({
          session_id: data.sessionId,
          influencer_id: data.influencerId,
          user_message: data.userMessage,
          ai_response: data.aiResponse,
          products_recommended: data.productsRecommended,
          response_time_ms: data.responseTime
        })

      if (error) throw error
    } catch (error) {
      console.error('Error logging chat session:', error)
    }
  }

  private getFallbackResponse(influencerId: string): ChatResponse {
    const fallbackResponses = {
      'alix-earle': "Hey babe! I'm having some technical difficulties right now, but I'm so excited to help you find the perfect pieces! Try asking me again in a moment! 💕✨",
      'emma-chamberlain': "Ugh okay so like... my brain is literally not working right now. But I want to help you find the cutest stuff! Give me a sec and try again? ☕️",
      'style-maven': "I apologize, darling, but I'm experiencing some technical difficulties at the moment. Please try again shortly, and I'll be delighted to assist you with your style needs. 🖤"
    }

    return {
      content: fallbackResponses[influencerId as keyof typeof fallbackResponses] || 
               "Sorry, I'm having some technical issues! Please try again in a moment.",
      products: [],
      confidence: 0.3,
      messageId: nanoid()
    }
  }

  private extractOccasion(message: string): string {
    const occasions = {
      'beach': ['beach', 'vacation', 'resort', 'swimwear', 'pool'],
      'date': ['date', 'romantic', 'dinner', 'evening'],
      'work': ['work', 'office', 'professional', 'business'],
      'casual': ['casual', 'weekend', 'everyday', 'comfortable'],
      'formal': ['formal', 'dressy', 'elegant', 'special event'],
      'party': ['party', 'night out', 'clubbing', 'drinks']
    }

    const lowerMessage = message.toLowerCase()
    
    for (const [occasion, keywords] of Object.entries(occasions)) {
      if (keywords.some(keyword => lowerMessage.includes(keyword))) {
        return occasion
      }
    }
    
    return 'casual'
  }

  private extractSimpleKeywords(message: string): string[] {
    const lowerMessage = message.toLowerCase()
    const keywords: string[] = []

    const styleWords = ['trendy', 'casual', 'formal', 'chic', 'edgy', 'boho', 'minimalist', 'vintage']
    styleWords.forEach(word => {
      if (lowerMessage.includes(word)) keywords.push(word)
    })

    const occasionWords = ['beach', 'work', 'date', 'party', 'weekend', 'vacation']
    occasionWords.forEach(word => {
      if (lowerMessage.includes(word)) keywords.push(word)
    })

    const clothingWords = ['dress', 'top', 'pants', 'skirt', 'jacket', 'shoes', 'bag', 'accessories']
    clothingWords.forEach(word => {
      if (lowerMessage.includes(word)) keywords.push(word)
    })

    return keywords
  }
}

export const enhancedChatService = new EnhancedChatService()