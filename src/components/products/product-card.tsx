"use client"

import { motion } from "framer-motion"
import { ExternalLink, Star, Heart } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import { ProductCardProps } from "@/types"
import { formatPrice } from "@/lib/utils"
import Image from "next/image"

export function ProductCard({ product, recommendation, onProductClick }: ProductCardProps) {
  const handleProductClick = () => {
    onProductClick?.(product)
    // Track affiliate click
    if (product.affiliateUrl) {
      window.open(product.affiliateUrl, '_blank', 'noopener,noreferrer')
    }
  }

  return (
    <Card className="group overflow-hidden transition-all duration-200 hover:shadow-lg">
      <CardContent className="p-3">
        <div className="flex items-start space-x-3">
          {/* Product Image */}
          <div className="relative flex-shrink-0">
            <Image
              src={product.image}
              alt={product.name}
              width={80}
              height={80}
              className="w-20 h-20 rounded-lg object-cover"
            />
            {recommendation && (
              <Badge 
                className="absolute -top-1 -right-1 text-xs px-1 py-0 bg-primary/90"
              >
                AI Pick
              </Badge>
            )}
          </div>
          
          {/* Product Details */}
          <div className="flex-1 min-w-0">
            <div className="flex items-start justify-between mb-1">
              <h4 className="font-medium text-sm text-foreground line-clamp-2 group-hover:text-primary transition-colors">
                {product.name}
              </h4>
              <button className="p-1 hover:bg-muted rounded-full transition-colors ml-2">
                <Heart className="h-4 w-4 text-muted-foreground hover:text-red-500" />
              </button>
            </div>
            
            <p className="text-xs text-muted-foreground mb-2">{product.brand}</p>
            
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <p className="font-semibold text-primary">{formatPrice(product.price, product.currency)}</p>
                <div className="flex items-center gap-1">
                  <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                  <span className="text-xs text-muted-foreground">
                    {product.rating} ({product.reviewCount})
                  </span>
                </div>
              </div>
              
              <Button 
                size="sm" 
                variant="outline"
                onClick={handleProductClick}
                className="h-8 px-3 text-xs"
              >
                <ExternalLink className="h-3 w-3 mr-1" />
                Shop
              </Button>
            </div>
            
            {recommendation && (
              <div className="mt-2 p-2 bg-primary/5 rounded-lg">
                <p className="text-xs text-primary font-medium">
                  💡 {recommendation.reason}
                </p>
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}