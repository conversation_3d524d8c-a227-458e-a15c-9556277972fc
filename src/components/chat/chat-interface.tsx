"use client"

import { useState, useRef, useEffect } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { MessageBubble } from "./message-bubble"
import { ChatInput } from "./chat-input"
import { TypingIndicator } from "./typing-indicator"
import { useChatStore } from "../../lib/store/chat-store"
import { Influencer, Message } from "../../types"
import { nanoid } from "nanoid"

interface ChatInterfaceProps {
  influencer: Influencer
}

export function ChatInterface({ influencer }: ChatInterfaceProps) {
  const { 
    conversations, 
    currentConversation, 
    isLoading, 
    setCurrentConversation, 
    addMessage, 
    setLoading,
    createConversation 
  } = useChatStore()
  
  const [isTyping, setIsTyping] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  
  // Initialize conversation
  useEffect(() => {
    const existingConversation = Object.values(conversations).find(
      conv => conv.influencerId === influencer.id
    )
    
    if (existingConversation) {
      setCurrentConversation(existingConversation)
    } else {
      const newConversation = createConversation(influencer.id)
      setCurrentConversation(newConversation)
      
      // Add welcome message
      const welcomeMessage: Message = {
        id: nanoid(),
        senderId: influencer.id,
        senderType: 'ai',
        content: `Hey babe! 💕 It's ${influencer.name} here! I'm so excited to help you find the perfect pieces for your wardrobe. What kind of look are you going for today?`,
        timestamp: new Date(),
        products: []
      }
      
      addMessage(newConversation.id, welcomeMessage)
    }
  }, [influencer.id, conversations, setCurrentConversation, createConversation, addMessage, influencer.name])
  
  // Auto scroll to bottom
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" })
  }, [currentConversation?.messages, isTyping])
  
  const handleSendMessage = async (content: string) => {
    if (!currentConversation || !content.trim()) return
    
    // Add user message
    const userMessage: Message = {
      id: nanoid(),
      senderId: 'user',
      senderType: 'user',
      content: content.trim(),
      timestamp: new Date(),
      products: []
    }
    
    addMessage(currentConversation.id, userMessage)
    setIsTyping(true)
    setLoading(true)
    
    try {
      // Call API
      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: content.trim(),
          influencerId: influencer.id,
          conversationId: currentConversation.id,
        }),
      })
      
      const data = await response.json()
      
      if (data.success) {
        // Add AI response
        const aiMessage: Message = {
          id: data.data.messageId,
          senderId: influencer.id,
          senderType: 'ai',
          content: data.data.content,
          timestamp: new Date(),
          products: data.data.products || []
        }
        
        // Simulate typing delay
        setTimeout(() => {
          addMessage(currentConversation.id, aiMessage)
          setIsTyping(false)
        }, 1000 + Math.random() * 1000)
      } else {
        throw new Error(data.error?.message || 'Failed to send message')
      }
    } catch (error) {
      console.error('Chat error:', error)
      
      // Add error message
      const errorMessage: Message = {
        id: nanoid(),
        senderId: influencer.id,
        senderType: 'ai',
        content: "Sorry babe, I'm having some technical difficulties! Try asking me again in a moment. 💕",
        timestamp: new Date(),
        products: []
      }
      
      setTimeout(() => {
        addMessage(currentConversation.id, errorMessage)
        setIsTyping(false)
      }, 1000)
    } finally {
      setLoading(false)
    }
  }
  
  return (
    <div className="flex flex-col h-[600px] bg-background">
      {/* Chat Header */}
      <div className="flex items-center gap-3 p-4 border-b bg-muted/30">
        <div className="relative">
          <img
            src={influencer.avatar}
            alt={influencer.name}
            className="w-10 h-10 rounded-full"
          />
          <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-background" />
        </div>
        <div className="flex-1">
          <h3 className="font-semibold">{influencer.name}</h3>
          <p className="text-xs text-muted-foreground">Online now • Responds quickly</p>
        </div>
        <div className="text-right">
          <div className="text-xs text-muted-foreground">AI-Powered</div>
          <div className="text-xs text-primary font-medium">Style Expert</div>
        </div>
      </div>
      
      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        <AnimatePresence>
          {currentConversation?.messages.map((message) => (
            <motion.div
              key={message.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
            >
              <MessageBubble
                message={message}
                influencer={influencer}
                isOwn={message.senderType === 'user'}
              />
            </motion.div>
          ))}
        </AnimatePresence>
        
        {isTyping && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
          >
            <TypingIndicator influencer={influencer} />
          </motion.div>
        )}
        
        <div ref={messagesEndRef} />
      </div>
      
      {/* Chat Input */}
      <div className="border-t bg-muted/30">
        <ChatInput 
          onSendMessage={handleSendMessage}
          isLoading={isLoading}
          disabled={isTyping}
        />
      </div>
    </div>
  )
}