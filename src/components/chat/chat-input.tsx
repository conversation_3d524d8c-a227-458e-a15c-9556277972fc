"use client"

import { useState } from "react"
import { Send, Loader2 } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { ChatInputProps } from "@/types"

export function ChatInput({ onSendMessage, isLoading, disabled }: ChatInputProps) {
  const [message, setMessage] = useState("")

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (!message.trim() || disabled) return
    
    onSendMessage(message.trim())
    setMessage("")
  }

  const suggestions = [
    "I need an outfit for a date night",
    "Summer vacation essentials",
    "Work-appropriate but stylish",
    "Casual weekend vibes",
    "Something for a special event"
  ]

  return (
    <div className="p-4 space-y-3">
      {/* Quick Suggestions */}
      {!isLoading && (
        <div className="flex flex-wrap gap-2">
          {suggestions.slice(0, 3).map((suggestion) => (
            <button
              key={suggestion}
              onClick={() => setMessage(suggestion)}
              className="px-3 py-1 text-xs bg-primary/10 text-primary rounded-full hover:bg-primary/20 transition-colors"
            >
              {suggestion}
            </button>
          ))}
        </div>
      )}
      
      <form onSubmit={handleSubmit} className="flex items-center space-x-2">
        <div className="flex-1 relative">
          <Input
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            placeholder="Ask for style recommendations..."
            disabled={disabled}
            className="pr-12 rounded-full"
          />
          <Button
            type="submit"
            size="icon"
            disabled={!message.trim() || disabled}
            className="absolute right-1 top-1/2 transform -translate-y-1/2 h-8 w-8 rounded-full"
          >
            {isLoading ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Send className="h-4 w-4" />
            )}
          </Button>
        </div>
      </form>
      
      <div className="flex items-center justify-center">
        <div className="flex items-center space-x-2 text-xs text-muted-foreground">
          <span>✨</span>
          <span>Powered by AI • Personalized recommendations</span>
        </div>
      </div>
    </div>
  )
}