"use client"

import { motion } from "framer-motion"
import { ProductCard } from "@/components/products/product-card"
import { MessageBubbleProps } from "@/types"
import { formatTime } from "@/lib/utils/date-utils"

export function MessageBubble({ message, influencer, isOwn }: MessageBubbleProps) {
  return (
    <div className={`flex ${isOwn ? 'justify-end' : 'justify-start'}`}>
      <div className={`max-w-xs lg:max-w-md ${isOwn ? 'order-2' : 'order-1'}`}>
        {!isOwn && (
          <div className="flex items-center space-x-2 mb-1">
            <img
              src={influencer.avatar}
              alt={influencer.name}
              className="w-6 h-6 rounded-full"
            />
            <span className="text-sm font-medium text-muted-foreground">{influencer.name}</span>
            <span className="text-xs text-muted-foreground">{formatTime(message.timestamp)}</span>
          </div>
        )}
        
        <motion.div
          initial={{ scale: 0.95 }}
          animate={{ scale: 1 }}
          className={`rounded-2xl px-4 py-3 ${
            isOwn
              ? 'bg-primary text-primary-foreground ml-4'
              : 'bg-muted text-foreground mr-4'
          }`}
        >
          <p className="text-sm leading-relaxed whitespace-pre-wrap">{message.content}</p>
        </motion.div>
        
        {message.products && message.products.length > 0 && (
          <div className="mt-3 space-y-2">
            {message.products.map((productRec, idx) => (
              <motion.div
                key={productRec.product.id}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: idx * 0.1 }}
              >
                <ProductCard 
                  product={productRec.product} 
                  recommendation={productRec}
                />
              </motion.div>
            ))}
          </div>
        )}
        
        {isOwn && (
          <div className="text-xs text-muted-foreground mt-1 text-right">
            {formatTime(message.timestamp)}
          </div>
        )}
      </div>
    </div>
  )
}