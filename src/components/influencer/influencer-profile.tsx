"use client"

import { motion } from "framer-motion"
import { Star, Users, MessageCircle, ExternalLink } from "lucide-react"
import { <PERSON><PERSON> } from "../ui/button"
import { Badge } from "../ui/badge"
import { Card, CardContent } from "../ui/card"
import { Influencer } from "../../types"
import Image from "next/image"

interface InfluencerProfileProps {
  influencer: Influencer
}

export function InfluencerProfile({ influencer }: InfluencerProfileProps) {
  return (
    <Card className="overflow-hidden">
      {/* Cover Image */}
      <div className="h-48 bg-gradient-to-r from-primary/20 to-purple-500/20 relative">
        <Image
          src={influencer.coverImage}
          alt={`${influencer.name} cover`}
          fill
          className="object-cover"
        />
        <div className="absolute inset-0 bg-black/20" />
        
        {/* Floating Stats */}
        <div className="absolute top-4 right-4 flex gap-2">
          {influencer.verified && (
            <Badge className="bg-white/20 text-white border-white/30">
              ✓ Verified
            </Badge>
          )}
          <Badge className="bg-white/20 text-white border-white/30">
            ⭐ {influencer.rating}
          </Badge>
        </div>
      </div>
      
      <CardContent className="p-8">
        {/* Avatar and Basic Info - Always Centered */}
        <div className="flex flex-col items-center text-center space-y-4 mb-8">
          <div className="relative">
            <Image
              src={influencer.avatar}
              alt={influencer.name}
              width={120}
              height={120}
              className="w-30 h-30 rounded-full border-4 border-background shadow-xl -mt-16 relative z-10"
            />
            {influencer.isActive && (
              <div className="absolute -bottom-2 -right-2 w-6 h-6 bg-green-500 rounded-full border-4 border-background" />
            )}
          </div>

          <div className="space-y-2">
            <h1 className="text-3xl font-bold">{influencer.name}</h1>
            <p className="text-lg text-primary font-medium">{influencer.specialty}</p>
            <p className="text-muted-foreground">@{influencer.username}</p>
          </div>

          {/* Social Links */}
          <div className="flex items-center gap-3">
            {influencer.socialLinks.instagram && (
              <a
                href={influencer.socialLinks.instagram}
                target="_blank"
                rel="noopener noreferrer"
                className="p-2 bg-muted hover:bg-muted/80 rounded-full transition-colors"
              >
                <span className="text-lg">📸</span>
              </a>
            )}
            {influencer.socialLinks.tiktok && (
              <a
                href={influencer.socialLinks.tiktok}
                target="_blank"
                rel="noopener noreferrer"
                className="p-2 bg-muted hover:bg-muted/80 rounded-full transition-colors"
              >
                <span className="text-lg">🎵</span>
              </a>
            )}
            {influencer.socialLinks.youtube && (
              <a
                href={influencer.socialLinks.youtube}
                target="_blank"
                rel="noopener noreferrer"
                className="p-2 bg-muted hover:bg-muted/80 rounded-full transition-colors"
              >
                <span className="text-lg">📺</span>
              </a>
            )}
          </div>
        </div>

        {/* Content - Centered */}
        <div className="space-y-6 max-w-md mx-auto">
            {/* Bio */}
            <div>
              <p className="text-lg leading-relaxed text-muted-foreground">
                {influencer.bio}
              </p>
            </div>
            
            {/* Stats Grid */}
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-3">
                <div className="bg-muted/30 rounded-lg p-3 text-center">
                  <Users className="w-5 h-5 mx-auto mb-1 text-primary" />
                  <div className="text-lg font-bold">{influencer.followers}</div>
                  <div className="text-xs text-muted-foreground">Followers</div>
                </div>
                <div className="bg-muted/30 rounded-lg p-3 text-center">
                  <Star className="w-5 h-5 mx-auto mb-1 text-yellow-500" />
                  <div className="text-lg font-bold">{influencer.rating}</div>
                  <div className="text-xs text-muted-foreground">Rating</div>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-3">
                <div className="bg-muted/30 rounded-lg p-3 text-center">
                  <MessageCircle className="w-5 h-5 mx-auto mb-1 text-green-500" />
                  <div className="text-lg font-bold">{influencer.totalRecommendations}</div>
                  <div className="text-xs text-muted-foreground">Recommendations</div>
                </div>
                <div className="bg-muted/30 rounded-lg p-3 text-center">
                  <ExternalLink className="w-5 h-5 mx-auto mb-1 text-blue-500" />
                  <div className="text-lg font-bold">Active</div>
                  <div className="text-xs text-muted-foreground">Status</div>
                </div>
              </div>
            </div>
            
            {/* Categories */}
            <div>
              <h3 className="text-lg font-semibold mb-3">Style Categories</h3>
              <div className="flex flex-wrap gap-2">
                {influencer.categories.map((category) => (
                  <Badge key={category} variant="secondary">
                    {category}
                  </Badge>
                ))}
              </div>
            </div>
            
            {/* Style Keywords */}
            <div>
              <h3 className="text-lg font-semibold mb-3">Style Vibes</h3>
              <div className="flex flex-wrap gap-2">
                {influencer.styleKeywords.map((keyword) => (
                  <Badge key={keyword} variant="outline">
                    {keyword}
                  </Badge>
                ))}
              </div>
            </div>
        </div>
      </CardContent>
    </Card>
  )
}