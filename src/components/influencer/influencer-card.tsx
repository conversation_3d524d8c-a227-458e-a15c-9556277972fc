"use client"

import { motion } from "framer-motion"
import { MessageCircle, Users, Star, ExternalLink } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import { Influencer } from "@/types"
import Link from "next/link"
import Image from "next/image"

interface InfluencerCardProps {
  influencer: Influencer
  onClick?: (influencer: Influencer) => void
}

export function InfluencerCard({ influencer, onClick }: InfluencerCardProps) {
  return (
    <Card className="group overflow-hidden cursor-pointer transition-all duration-300 hover:shadow-xl hover:-translate-y-1">
      <div className={`h-32 bg-gradient-to-r from-primary/20 to-purple-500/20 relative`}>
        <div className="absolute inset-0 bg-black/10" />
        <div className="absolute bottom-4 left-6">
          <div className="flex items-center space-x-2 text-foreground">
            <Users className="h-4 w-4" />
            <span className="text-sm font-medium">{influencer.followers} followers</span>
          </div>
        </div>
        {influencer.verified && (
          <div className="absolute top-4 right-4">
            <Badge variant="secondary" className="bg-white/20 text-foreground border-white/30">
              ✓ Verified
            </Badge>
          </div>
        )}
      </div>
      
      <CardContent className="p-6">
        <div className="flex items-center space-x-4 mb-4">
          <div className="relative">
            <Image
              src={influencer.avatar}
              alt={influencer.name}
              width={64}
              height={64}
              className="w-16 h-16 rounded-full border-4 border-background shadow-lg -mt-8 relative z-10"
            />
            {influencer.isActive && (
              <div className="absolute -bottom-1 -right-1 w-5 h-5 bg-green-500 rounded-full border-2 border-background" />
            )}
          </div>
          <div className="flex-1">
            <h3 className="text-xl font-bold text-foreground group-hover:text-primary transition-colors">
              {influencer.name}
            </h3>
            <p className="text-primary font-medium">{influencer.specialty}</p>
          </div>
          <div className="flex items-center gap-1">
            <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
            <span className="text-sm font-medium">{influencer.rating}</span>
          </div>
        </div>
        
        <p className="text-muted-foreground mb-4 text-sm leading-relaxed line-clamp-2">
          {influencer.bio}
        </p>
        
        <div className="flex flex-wrap gap-2 mb-4">
          {influencer.categories.slice(0, 3).map((category, idx) => (
            <Badge
              key={idx}
              variant="outline"
              className="text-xs"
            >
              {category}
            </Badge>
          ))}
          {influencer.categories.length > 3 && (
            <Badge variant="outline" className="text-xs">
              +{influencer.categories.length - 3}
            </Badge>
          )}
        </div>
        
        <div className="flex items-center justify-between text-sm text-muted-foreground mb-4">
          <span>{influencer.totalRecommendations} recommendations</span>
          <div className="flex items-center gap-2">
            {influencer.socialLinks.instagram && (
              <a href={influencer.socialLinks.instagram} target="_blank" rel="noopener noreferrer" className="hover:text-primary transition-colors">
                📸
              </a>
            )}
            {influencer.socialLinks.tiktok && (
              <a href={influencer.socialLinks.tiktok} target="_blank" rel="noopener noreferrer" className="hover:text-primary transition-colors">
                🎵
              </a>
            )}
            {influencer.socialLinks.youtube && (
              <a href={influencer.socialLinks.youtube} target="_blank" rel="noopener noreferrer" className="hover:text-primary transition-colors">
                📺
              </a>
            )}
          </div>
        </div>
        
        <Button 
          className="w-full group/btn" 
          onClick={() => onClick?.(influencer)}
          asChild
        >
          <Link href={`/influencer/${influencer.id}`}>
            <MessageCircle className="mr-2 h-4 w-4" />
            Start Shopping
            <ExternalLink className="ml-2 h-4 w-4 opacity-0 group-hover/btn:opacity-100 transition-opacity" />
          </Link>
        </Button>
      </CardContent>
    </Card>
  )
}