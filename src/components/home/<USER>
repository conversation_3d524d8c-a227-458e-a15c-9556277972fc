"use client"

import { useState, useEffect, useRef } from "react"
import { motion, useScroll, useTransform } from "framer-motion"
import { Search, ChevronLeft, ChevronRight, Sparkles, TrendingUp, Star, Users, MessageCircle } from "lucide-react"
import { InfluencerCard } from "@/components/influencer/influencer-card"
import { getInfluencers } from "@/lib/data/influencers"
import { Influencer } from "@/types"
import { Button } from "../ui/button"
import { Input } from "../ui/input"
import { Badge } from "../ui/badge"
import Link from "next/link"

export function FeaturedInfluencers() {
  const [influencers, setInfluencers] = useState<Influencer[]>([])
  const [filteredInfluencers, setFilteredInfluencers] = useState<Influencer[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState("")
  const [currentIndex, setCurrentIndex] = useState(0)
  const scrollRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    async function loadInfluencers() {
      try {
        const data = await getInfluencers()
        setInfluencers(data)
        setFilteredInfluencers(data)
      } catch (error) {
        console.error('Error loading influencers:', error)
      } finally {
        setLoading(false)
      }
    }

    loadInfluencers()
  }, [])

  // Filter influencers based on search query
  useEffect(() => {
    if (!searchQuery.trim()) {
      setFilteredInfluencers(influencers)
    } else {
      const filtered = influencers.filter(influencer =>
        influencer.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        influencer.specialty.toLowerCase().includes(searchQuery.toLowerCase()) ||
        influencer.categories.some(cat => 
          cat.toLowerCase().includes(searchQuery.toLowerCase())
        ) ||
        influencer.styleKeywords.some(keyword => 
          keyword.toLowerCase().includes(searchQuery.toLowerCase())
        )
      )
      setFilteredInfluencers(filtered)
    }
  }, [searchQuery, influencers])

  const scrollToCard = (index: number) => {
    const container = scrollRef.current
    if (!container) return

    const cardWidth = 320 + 32 // card width + gap
    const containerWidth = container.clientWidth
    const scrollPosition = index * cardWidth - (containerWidth / 2) + (cardWidth / 2)
    
    container.scrollTo({
      left: Math.max(0, scrollPosition),
      behavior: 'smooth'
    })
    setCurrentIndex(index)
  }

  const nextCard = () => {
    const nextIndex = (currentIndex + 1) % filteredInfluencers.length
    scrollToCard(nextIndex)
  }

  const prevCard = () => {
    const prevIndex = currentIndex === 0 ? filteredInfluencers.length - 1 : currentIndex - 1
    scrollToCard(prevIndex)
  }

  if (loading) {
    return (
      <div className="space-y-16">
        <div className="text-center space-y-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="space-y-6"
          >
            <div className="h-8 bg-gradient-to-r from-muted via-muted/50 to-muted rounded-lg w-48 mx-auto animate-pulse" />
            <div className="h-16 bg-gradient-to-r from-muted via-muted/50 to-muted rounded-lg w-96 mx-auto animate-pulse" />
            <div className="h-6 bg-gradient-to-r from-muted via-muted/50 to-muted rounded-lg w-80 mx-auto animate-pulse" />
          </motion.div>

          <div className="max-w-2xl mx-auto">
            <div className="h-14 bg-gradient-to-r from-muted via-muted/50 to-muted rounded-2xl animate-pulse" />
          </div>
        </div>

        <div className="flex gap-8 overflow-x-auto px-8">
          {[1, 2, 3].map((i) => (
            <motion.div
              key={i}
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: i * 0.1 }}
              className="flex-shrink-0 w-80"
            >
              <div className="bg-card rounded-3xl p-6 space-y-4 shadow-xl">
                <div className="h-32 bg-gradient-to-r from-muted via-muted/50 to-muted rounded-2xl animate-pulse" />
                <div className="flex items-center space-x-4">
                  <div className="w-16 h-16 bg-gradient-to-r from-muted via-muted/50 to-muted rounded-full animate-pulse" />
                  <div className="space-y-2 flex-1">
                    <div className="h-4 bg-gradient-to-r from-muted via-muted/50 to-muted rounded-lg w-24 animate-pulse" />
                    <div className="h-3 bg-gradient-to-r from-muted via-muted/30 to-muted rounded-lg w-16 animate-pulse" />
                  </div>
                </div>
                <div className="space-y-3">
                  <div className="h-3 bg-gradient-to-r from-muted via-muted/30 to-muted rounded-lg w-full animate-pulse" />
                  <div className="h-3 bg-gradient-to-r from-muted via-muted/30 to-muted rounded-lg w-3/4 animate-pulse" />
                </div>
                <div className="h-10 bg-gradient-to-r from-muted via-muted/50 to-muted rounded-2xl animate-pulse" />
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="relative overflow-hidden">
      <div className="relative z-10 space-y-16">
        {/* Header Section */}
        <div className="text-center space-y-8">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="space-y-6"
          >
            <div className="flex items-center justify-center gap-2 mb-4">
              <Badge variant="secondary" className="px-4 py-2 bg-primary/10 text-primary border-primary/20">
                <Sparkles className="w-4 h-4 mr-2" />
                AI-Powered Shopping
              </Badge>
            </div>

            <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold tracking-tight">
              Shop with{" "}
              <span className="relative">
                <span className="bg-gradient-to-r from-primary via-purple-600 to-blue-600 bg-clip-text text-transparent">
                  Top Influencers
                </span>
                <motion.div
                  className="absolute -bottom-2 left-0 right-0 h-1 bg-gradient-to-r from-primary/30 via-purple-600/30 to-blue-600/30 rounded-full"
                  initial={{ scaleX: 0 }}
                  whileInView={{ scaleX: 1 }}
                  transition={{ duration: 1, delay: 0.5 }}
                />
              </span>
            </h1>
            
            <p className="text-xl md:text-2xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
              Get personalized style recommendations from verified influencers using advanced AI technology
            </p>
          </motion.div>

          {/* Search Bar */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            className="max-w-2xl mx-auto"
          >
            <div className="relative group">
              <Search className="absolute left-6 top-1/2 transform -translate-y-1/2 text-muted-foreground/60 h-5 w-5 transition-colors group-focus-within:text-primary" />
              <Input
                placeholder="Search by name, style, or category..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-14 pr-6 py-4 text-lg bg-background/80 backdrop-blur-sm border-2 border-border/50 focus:border-primary/50 rounded-2xl shadow-lg transition-all duration-300 hover:shadow-xl focus:shadow-xl"
              />
              <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-primary/5 to-purple-500/5 opacity-0 group-focus-within:opacity-100 transition-opacity duration-300 pointer-events-none" />
            </div>
            
            {/* Search Stats */}
            <div className="flex items-center justify-center gap-6 mt-4 text-sm text-muted-foreground">
              <div className="flex items-center gap-2">
                <TrendingUp className="w-4 h-4" />
                <span>{filteredInfluencers.length} Influencers</span>
              </div>
              <div className="w-1 h-1 bg-muted-foreground/30 rounded-full" />
              <span>AI-Powered Recommendations</span>
              <div className="w-1 h-1 bg-muted-foreground/30 rounded-full" />
              <span>Verified Profiles</span>
            </div>
          </motion.div>
        </div>

        {/* Influencer Carousel */}
        {filteredInfluencers.length > 0 ? (
          <motion.div
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="relative"
          >
            {/* Navigation Buttons */}
            <div className="absolute left-4 top-1/2 -translate-y-1/2 z-20">
              <Button
                variant="outline"
                size="icon"
                onClick={prevCard}
                className="h-12 w-12 rounded-full bg-background/80 backdrop-blur-sm border-2 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110"
              >
                <ChevronLeft className="h-5 w-5" />
              </Button>
            </div>
            
            <div className="absolute right-4 top-1/2 -translate-y-1/2 z-20">
              <Button
                variant="outline"
                size="icon"
                onClick={nextCard}
                className="h-12 w-12 rounded-full bg-background/80 backdrop-blur-sm border-2 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110"
              >
                <ChevronRight className="h-5 w-5" />
              </Button>
            </div>

            {/* Carousel Container */}
            <div
              ref={scrollRef}
              className="flex gap-8 overflow-x-auto scrollbar-hide px-8 py-8"
              style={{
                scrollBehavior: 'smooth',
                scrollSnapType: 'x mandatory'
              }}
            >
              {filteredInfluencers.map((influencer, index) => (
                <motion.div
                  key={influencer.id}
                  className="flex-shrink-0 w-80"
                  style={{ scrollSnapAlign: 'center' }}
                  initial={{ opacity: 0, y: 50 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                >
                  <div className="relative group">
                    {/* Enhanced Card with 3D Effect */}
                    <div className="relative transform transition-all duration-500 hover:scale-105 hover:-rotate-1">
                      {/* Glow Effect */}
                      <div className="absolute -inset-1 bg-gradient-to-r from-primary/20 via-purple-500/20 to-blue-500/20 rounded-3xl blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
                      
                      {/* Card Content */}
                      <div className="relative bg-card/80 backdrop-blur-sm border-2 border-border/50 rounded-3xl overflow-hidden shadow-xl group-hover:shadow-2xl transition-all duration-500">
                        <InfluencerCard influencer={influencer} />
                      </div>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>

            {/* Carousel Indicators */}
            <div className="flex justify-center gap-2 mt-8">
              {filteredInfluencers.slice(0, 5).map((_, index) => (
                <button
                  key={index}
                  onClick={() => scrollToCard(index)}
                  className={`w-3 h-3 rounded-full transition-all duration-300 ${
                    index === currentIndex % 5
                      ? 'bg-primary scale-110'
                      : 'bg-muted-foreground/30 hover:bg-muted-foreground/50'
                  }`}
                />
              ))}
            </div>
          </motion.div>
        ) : (
          <NoResultsState searchQuery={searchQuery} />
        )}
      </div>

      {/* Custom Scrollbar Styles */}
      <style jsx>{`
        .scrollbar-hide {
          -ms-overflow-style: none;
          scrollbar-width: none;
        }
        .scrollbar-hide::-webkit-scrollbar {
          display: none;
        }
      `}</style>
    </div>
  )
}

// No Results State Component
function NoResultsState({ searchQuery }: { searchQuery: string }) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="text-center py-16"
    >
      <div className="w-24 h-24 bg-muted/30 rounded-full flex items-center justify-center mx-auto mb-6">
        <Search className="w-12 h-12 text-muted-foreground/50" />
      </div>
      <h3 className="text-xl font-semibold mb-2">No influencers found</h3>
      <p className="text-muted-foreground mb-6">
        {searchQuery ? (
          <>No results for "<span className="font-medium">{searchQuery}</span>". Try a different search term.</>
        ) : (
          "No influencers available. Add some in the admin dashboard!"
        )}
      </p>
      <Button variant="outline" asChild>
        <Link href="/admin">
          Add Influencers
        </Link>
      </Button>
    </motion.div>
  )
}